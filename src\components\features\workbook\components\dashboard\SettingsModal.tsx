import React, { useState, useEffect } from 'react';
import { IoClose } from 'react-icons/io5';
import { MdOutlineSave } from 'react-icons/md';
import { Radio } from '@base-ui-components/react/radio';
import { RadioGroup } from '@base-ui-components/react/radio-group';
import './SettingsModal.scss';

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (temperature: string) => void;
}

type TemperatureOption = 'less-creative' | 'classic' | 'more-creative';

const SettingsModal: React.FC<SettingsModalProps> = ({ isOpen, onClose, onSave }) => {
  const [selectedTemperature, setSelectedTemperature] = useState<TemperatureOption>('classic');

  // Handle ESC key press
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleSave = () => {
    onSave(selectedTemperature);
    onClose();
  };

  const handleCancel = () => {
    setSelectedTemperature('classic'); // Reset to default
    onClose();
  };

  return (
    <div className="settings-modal-overlay" onClick={handleOverlayClick}>
      {/* Modal Content */}
      <div className="settings-modal" onClick={(e) => e.stopPropagation()}>
        {/* Close Button */}
        <button
          onClick={onClose}
          className="settings-modal__close-btn"
          aria-label="Close modal"
        >
          <IoClose className="w-6 h-6" />
        </button>

        {/* Content */}
        <div className="settings-modal__content">
          {/* Title */}
          <h1 className="settings-modal__title">Setting</h1>
          
          {/* Temperature Section */}
          <div className="settings-modal__temperature-section">
            {/* Temperature Title */}
            <h2 className="settings-modal__temperature-title">Temperature</h2>

            {/* Temperature Description */}
            <div className="settings-modal__temperature-description">
              Temperature is a parameter that allows you to control the creativity of Sidekick's generated output.
            </div>

            {/* Radio Button Group */}
            <div className="settings-modal__radio-group">
              <RadioGroup
                value={selectedTemperature}
                onValueChange={(value) => setSelectedTemperature(value as TemperatureOption)}
                className="flex items-center gap-6"
              >
                <label className="settings-modal__radio-option">
                  <Radio.Root value="less-creative" className="settings-modal__radio-input">
                    <Radio.Indicator className="settings-modal__radio-indicator" />
                  </Radio.Root>
                  <span>Less creative</span>
                </label>

                <label className="settings-modal__radio-option">
                  <Radio.Root value="classic" className="settings-modal__radio-input">
                    <Radio.Indicator className="settings-modal__radio-indicator" />
                  </Radio.Root>
                  <span>Classic</span>
                </label>

                <label className="settings-modal__radio-option">
                  <Radio.Root value="more-creative" className="settings-modal__radio-input">
                    <Radio.Indicator className="settings-modal__radio-indicator" />
                  </Radio.Root>
                  <span>More creative</span>
                </label>
              </RadioGroup>
            </div>
          </div>

          {/* Buttons */}
          <div className="settings-modal__actions">
            <button
              onClick={handleCancel}
              className="settings-modal__button settings-modal__button--cancel"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="settings-modal__button settings-modal__button--save"
            >
              <MdOutlineSave className="w-5 h-5" />
              <span>Save Settings</span>
            </button>
          </div>
        </div>

        {/* Footer */}
        <footer className="settings-modal__footer">
          Advanced settings guidelines go here.
        </footer>
      </div>
    </div>
  );
};

export default SettingsModal; 