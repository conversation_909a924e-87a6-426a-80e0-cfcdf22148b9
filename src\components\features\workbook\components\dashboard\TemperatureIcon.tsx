import React from 'react';

interface TemperatureIconProps {
  className?: string;
}

const TemperatureIcon: React.FC<TemperatureIconProps> = ({ className = '' }) => {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      width="24" 
      height="25" 
      viewBox="0 0 24 25" 
      fill="none"
      className={className}
    >
      <path 
        d="M15 13.5V5.5C15 3.84 13.66 2.5 12 2.5C10.34 2.5 9 3.84 9 5.5V13.5C7.79 14.41 7 15.87 7 17.5C7 20.26 9.24 22.5 12 22.5C14.76 22.5 17 20.26 17 17.5C17 15.87 16.21 14.41 15 13.5ZM11 11.5V5.5C11 4.95 11.45 4.5 12 4.5C12.55 4.5 13 4.95 13 5.5V6.5H12V7.5H13V9.5H12V10.5H13V11.5H11Z" 
        fill="white"
      />
    </svg>
  );
};

export default TemperatureIcon;
