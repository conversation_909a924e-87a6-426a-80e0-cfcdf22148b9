import React, { useState } from 'react';
import { FaArrowRight } from 'react-icons/fa';
import { IoMdSettings } from 'react-icons/io';
import { useThemeStyles } from '@/hooks/useThemeStyles';
import { InputLowerTrayProps } from '@features/workbook/workbookTypes';
import SettingsModal from './SettingsModal';
import { TemperatureIcon } from '@/components/common/icons';
import './InputLowerTray.scss';

const InputLowerTray: React.FC<InputLowerTrayProps> = ({ onSubmit }) => {
  const { classes } = useThemeStyles();
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [currentTemperature, setCurrentTemperature] = useState('Classic');

  const handleSettingsClick = () => {
    setIsSettingsModalOpen(true);
  };

  const handleSettingsModalClose = () => {
    setIsSettingsModalOpen(false);
  };

  const handleSettingsSave = (temperature: string) => {
    // Convert the temperature value to display format
    const temperatureDisplayMap = {
      'less-creative': 'Less Creative',
      classic: 'Classic',
      'more-creative': 'More Creative',
    };
    setCurrentTemperature(temperatureDisplayMap[temperature as keyof typeof temperatureDisplayMap] || 'Classic');
  };

  return (
    <>
      <div className="input-lower-tray">
        <div className="input-lower-tray__button-group">
          {/* Temperature Badge */}
          <div className="input-lower-tray__temperature-badge">
            <TemperatureIcon className="input-lower-tray__temperature-icon" />
            <span className="input-lower-tray__temperature-text">{currentTemperature}</span>
          </div>

          {/* Settings Button */}
          <button
            className={`input-lower-tray__settings-button ${classes.circleIconButton}`}
            onClick={handleSettingsClick}
            aria-label="Settings"
            tabIndex={0}
          >
            <IoMdSettings className="input-lower-tray__settings-icon" />
          </button>

          {/* Send Button */}
          <button
            className={`input-lower-tray__send-button ${classes.circleIconButton}`}
            onClick={onSubmit}
            aria-label="Send message"
            tabIndex={0}
          >
            <FaArrowRight className="input-lower-tray__send-icon" />
          </button>
        </div>
      </div>

      {/* Settings Modal */}
      <SettingsModal isOpen={isSettingsModalOpen} onClose={handleSettingsModalClose} onSave={handleSettingsSave} />
    </>
  );
};

export default InputLowerTray;
