import React, { useState } from 'react';
import { FaArrowRight } from 'react-icons/fa';
import { IoMdSettings } from 'react-icons/io';
import { useThemeStyles } from '@/hooks/useThemeStyles';
import { InputLowerTrayProps } from '@features/workbook/workbookTypes';
import SettingsModal from './SettingsModal';
import TemperatureIcon from './TemperatureIcon';
import './InputLowerTray.scss';

const InputLowerTray: React.FC<InputLowerTrayProps> = ({ onSubmit }) => {
  const { classes } = useThemeStyles();
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [currentTemperature, setCurrentTemperature] = useState('Classic');

  const handleSettingsClick = () => {
    setIsSettingsModalOpen(true);
  };

  const handleSettingsModalClose = () => {
    setIsSettingsModalOpen(false);
  };

  const handleSettingsSave = (temperature: string) => {
    // Convert the temperature value to display format
    const temperatureDisplayMap = {
      'less-creative': 'Less Creative',
      classic: 'Classic',
      'more-creative': 'More Creative',
    };
    setCurrentTemperature(temperatureDisplayMap[temperature as keyof typeof temperatureDisplayMap] || 'Classic');
  };

  return (
    <>
      <div className="input-lower-tray flex w-full items-center justify-end absolute bottom-3 right-6">
        <div className="input-lower-tray__button-group flex items-center gap-2">
          {/* Temperature Badge */}
          <div className="input-lower-tray__temperature-badge">
            <TemperatureIcon className="input-lower-tray__temperature-icon" />
            <span className="input-lower-tray__temperature-text">{currentTemperature}</span>
          </div>

          {/* Settings Button */}
          <button
            className={`input-lower-tray__settings-button ${classes.circleIconButton} font-roboto`}
            onClick={handleSettingsClick}
            aria-label="Settings"
            tabIndex={0}
          >
            <IoMdSettings className="input-lower-tray__settings-icon h-5 w-5" />
          </button>

          {/* Send Button */}
          <button
            className={`input-lower-tray__send-button ${classes.circleIconButton} font-roboto`}
            onClick={onSubmit}
            aria-label="Send message"
            tabIndex={0}
          >
            <FaArrowRight className="input-lower-tray__send-icon h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Settings Modal */}
      <SettingsModal isOpen={isSettingsModalOpen} onClose={handleSettingsModalClose} onSave={handleSettingsSave} />
    </>
  );
};

export default InputLowerTray;
