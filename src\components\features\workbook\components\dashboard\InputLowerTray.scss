.input-lower-tray {
  &__temperature-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    min-width: 120px; // Ensure enough space for longer text
    white-space: nowrap; // Prevent text wrapping
    
    // Ensure the badge can expand if needed
    flex-shrink: 0;
  }

  &__temperature-icon {
    flex-shrink: 0; // Prevent icon from shrinking
    width: 24px;
    height: 24px;
  }

  &__temperature-text {
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 500;
    color: white;
    line-height: 1;
    text-align: center;
    flex: 1;
    
    // Ensure text doesn't get cut off
    overflow: visible;
    text-overflow: clip;
  }

  &__button-group {
    display: flex;
    align-items: center;
    gap: 12px; // Increase gap between elements for better spacing
  }

  // Responsive adjustments for smaller screens
  @media (max-width: 768px) {
    &__temperature-badge {
      min-width: 100px;
      padding: 6px 10px;
    }
    
    &__temperature-text {
      font-size: 13px;
    }
  }
}
